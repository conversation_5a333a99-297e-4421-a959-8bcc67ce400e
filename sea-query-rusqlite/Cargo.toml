[workspace]
# A separate workspace

[package]
name = "sea-query-rusqlite"
version = "0.8.0-rc.8"
authors = [ "<PERSON> <<EMAIL>>" ]
edition = "2024"
description = "Binder traits for connecting sea-query with Rusqlite"
license = "MIT OR Apache-2.0"
documentation = "https://docs.rs/sea-query"
repository = "https://github.com/SeaQL/sea-query"
categories = [ "database" ]
keywords = [ "database", "sql", "sqlite" ]
rust-version = "1.85.0"

[lib]

[dependencies]
sea-query = { version = "1.0.0-rc.10", path = "..", default-features = false }
rusqlite = { version = "0.37" }

[features]
with-chrono = ["rusqlite/chrono", "sea-query/with-chrono"]
with-json = ["rusqlite/serde_json", "sea-query/with-json"]
with-rust_decimal = ["sea-query/with-rust_decimal"]
with-bigdecimal = ["sea-query/with-bigdecimal"]
with-uuid = ["rusqlite/uuid", "sea-query/with-uuid"]
with-time = ["rusqlite/time", "sea-query/with-time"]
with-ipnetwork = ["sea-query/with-ipnetwork"]
with-mac_address = ["sea-query/with-mac_address"]
postgres-array = ["sea-query/postgres-array"]
postgres-vector = ["sea-query/postgres-vector"]
