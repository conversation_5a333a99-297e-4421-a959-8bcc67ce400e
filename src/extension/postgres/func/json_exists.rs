use std::{borrow::Cow, fmt::Write};

use crate::{<PERSON>pr, Func, FunctionCall, PostgresQueryBuilder, QueryBuilder, Value, join_io};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Builder {
    pub context_item: Expr,
    pub path_expression: Cow<'static, str>,
    pub passing: Vec<(Value, Cow<'static, str>)>,
    pub on_error: Option<OnError>,
}

#[derive(Debu<PERSON>, <PERSON>lone)]
pub enum OnError {
    True,
    False,
    Unknown,
    Error,
}

impl Builder {
    pub fn build(self) -> FunctionCall {
        let mut buf = String::with_capacity(20);

        PostgresQueryBuilder.prepare_simple_expr(&self.context_item, &mut buf);
        buf.write_str(" '").unwrap();
        buf.write_str(&self.path_expression).unwrap();
        buf.write_str("'").unwrap();

        let mut piter = self.passing.into_iter();

        join_io!(
            piter,
            value_as,
            first {
                buf.write_str(" PASSING ").unwrap();
            },
            join {
                buf.write_str(", ").unwrap();
            },
            do {
                PostgresQueryBuilder.prepare_value(value_as.0, &mut buf);
                buf.write_str(" AS ").unwrap();
                buf.write_str(&value_as.1).unwrap();
            }
        );

        if let Some(on_error) = self.on_error {
            match on_error {
                OnError::True => buf.write_str(" TRUE").unwrap(),
                OnError::False => buf.write_str(" FALSE").unwrap(),
                OnError::Unknown => buf.write_str(" UNKNOWN").unwrap(),
                OnError::Error => buf.write_str(" ERROR").unwrap(),
            }
            buf.write_str(" ON ERROR").unwrap();
        }

        FunctionCall::new(Func::Custom("JSON_EXISTS".into())).arg(Expr::Custom(buf))
    }
}
