use sea_query::extension::postgres::*;
use sea_query::*;

#[test]
fn test_json_value_generic_returning() {
    // Test generic returning method with &str
    let query1 = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(r#""123.45""#), "$")
                .returning("float")
                .build(),
        )
        .to_owned();

    assert_eq!(
        query1.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE(E'\"123.45\"' '$' RETURNING float)"#
    );

    // Test generic returning method with String
    let query2 = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(r#""123.45""#), "$")
                .returning("decimal(10,2)".to_string())
                .build(),
        )
        .to_owned();

    assert_eq!(
        query2.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE(E'\"123.45\"' '$' RETURNING decimal(10,2))"#
    );
}

#[test]
fn test_json_value_convenience_returning_methods() {
    // Test returning_text
    let query1 = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(r#""hello""#), "$")
                .returning_text()
                .build(),
        )
        .to_owned();

    assert_eq!(
        query1.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE(E'\"hello\"' '$' RETURNING text)"#
    );

    // Test returning_integer
    let query2 = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(r#"42"#), "$")
                .returning_integer()
                .build(),
        )
        .to_owned();

    assert_eq!(
        query2.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE('42' '$' RETURNING integer)"#
    );

    // Test returning_boolean
    let query3 = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(r#"true"#), "$")
                .returning_boolean()
                .build(),
        )
        .to_owned();

    assert_eq!(
        query3.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE('true' '$' RETURNING boolean)"#
    );
}

#[test]
fn test_json_value_passing_many() {
    let query = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(r#"[1,2,3]"#), "strict $[$idx][$idx2]")
                .passing_many(vec![(1, "idx"), (2, "idx2")])
                .build(),
        )
        .to_owned();

    assert_eq!(
        query.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE('[1,2,3]' 'strict $[$idx][$idx2]' PASSING 1 AS idx, 2 AS idx2)"#
    );
}

#[test]
fn test_json_value_default_value_methods() {
    // Test default_value_on_empty
    let query1 = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(r#"{}"#), "$.missing")
                .default_value_on_empty("N/A")
                .build(),
        )
        .to_owned();

    assert_eq!(
        query1.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE('{}' '$.missing' DEFAULT 'N/A' ON EMPTY)"#
    );

    // Test default_value_on_error
    let query2 = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(r#"[1,2]"#), "strict $[*]")
                .default_value_on_error(0)
                .build(),
        )
        .to_owned();

    assert_eq!(
        query2.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE('[1,2]' 'strict $[*]' DEFAULT 0 ON ERROR)"#
    );
}

#[test]
fn test_json_value_chaining_methods() {
    // Test chaining multiple convenience methods
    let query = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(r#"{"data": [1,2,3]}"#), "$.data[$idx]")
                .passing(0, "idx")
                .returning_integer()
                .default_value_on_empty(-1)
                .null_on_error()
                .build(),
        )
        .to_owned();

    assert_eq!(
        query.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE(E'{\"data\": [1,2,3]}' '$.data[$idx]' PASSING 0 AS idx RETURNING integer DEFAULT -1 ON EMPTY NULL ON ERROR)"#
    );
}
